import axios from 'axios';
import { BASE_URL, RAZORPAY_APIKEYID } from '../config';
import { loadRazorpay } from '../utils/loadRazorpay';

export interface CreateOrderRequest {
  amount: number;
  receipt?: string;
  notes?: {
    course_id?: string;
    delegate_id?: string;
    course_title?: string;
    delegate_name?: string;
  };
}

export interface CreateOrderResponse {
  success: boolean;
  order: {
    id: string;
    entity: string;
    amount: number;
    amount_paid: number;
    amount_due: number;
    currency: string;
    receipt: string;
    status: string;
    created_at: number;
  };
  key_id: string;
  error?: string;
}

export interface VerifyPaymentRequest {
  razorpay_order_id: string;
  razorpay_payment_id: string;
  razorpay_signature: string;
  course_id?: string;
  delegate_id?: string;
}

export interface VerifyPaymentResponse {
  success: boolean;
  message?: string;
  payment_id?: string;
  order_id?: string;
  error?: string;
}

export interface RazorpayOptions {
  key: string;
  amount: number;
  currency: string;
  name: string;
  description: string;
  image?: string;
  order_id: string;
  handler: (response: any) => void;
  prefill: {
    name?: string;
    email?: string;
    contact?: string;
  };
  notes?: any;
  theme: {
    color: string;
  };
  modal?: {
    ondismiss?: () => void;
  };
}

export class PaymentService {
  private static instance: PaymentService;
  
  public static getInstance(): PaymentService {
    if (!PaymentService.instance) {
      PaymentService.instance = new PaymentService();
    }
    return PaymentService.instance;
  }

  // Create Razorpay Order
  async createOrder(orderData: CreateOrderRequest): Promise<CreateOrderResponse> {
    try {
      const response = await axios.post(`${BASE_URL}/payment/create-order`, orderData);
      return response.data;
    } catch (error: any) {
      console.error('Create order error:', error);
      throw new Error(error.response?.data?.error || 'Failed to create order');
    }
  }

  // Verify Payment
  async verifyPayment(paymentData: VerifyPaymentRequest): Promise<VerifyPaymentResponse> {
    try {
      const response = await axios.post(`${BASE_URL}/payment/verify-payment`, paymentData);
      return response.data;
    } catch (error: any) {
      console.error('Verify payment error:', error);
      throw new Error(error.response?.data?.error || 'Failed to verify payment');
    }
  }

  // Handle Payment Failure
  async handlePaymentFailure(errorData: any): Promise<void> {
    try {
      await axios.post(`${BASE_URL}/payment/payment-failed`, errorData);
    } catch (error) {
      console.error('Payment failure handling error:', error);
    }
  }

  // Initialize Razorpay Checkout
  async initializePayment(options: RazorpayOptions): Promise<void> {
    try {
      // Load Razorpay script
      const isLoaded = await loadRazorpay();
      
      if (!isLoaded) {
        throw new Error('Failed to load Razorpay SDK');
      }

      // Initialize Razorpay
      const razorpay = new (window as any).Razorpay(options);
      
      // Handle payment failure
      razorpay.on('payment.failed', async (response: any) => {
        console.error('Payment failed:', response.error);
        
        // Handle payment failure
        await this.handlePaymentFailure({
          error: response.error,
          order_id: options.order_id
        });
        
        // Call modal dismiss if provided
        if (options.modal?.ondismiss) {
          options.modal.ondismiss();
        }
      });

      // Open Razorpay checkout
      razorpay.open();
    } catch (error) {
      console.error('Payment initialization error:', error);
      throw error;
    }
  }

  // Complete Payment Flow
  async processPayment({
    amount,
    courseId,
    courseTitle,
    delegateId,
    delegateName,
    delegateEmail,
    delegateContact,
    onSuccess,
    onFailure
  }: {
    amount: number;
    courseId: string;
    courseTitle: string;
    delegateId: string;
    delegateName: string;
    delegateEmail?: string;
    delegateContact: string;
    onSuccess: (paymentData: any) => void;
    onFailure: (error: any) => void;
  }): Promise<void> {
    try {
      // Step 1: Create Order
      const orderResponse = await this.createOrder({
        amount,
        receipt: `course_${courseId}_${Date.now()}`,
        notes: {
          course_id: courseId,
          delegate_id: delegateId,
          course_title: courseTitle,
          delegate_name: delegateName
        }
      });

      if (!orderResponse.success) {
        throw new Error(orderResponse.error || 'Failed to create order');
      }

      // Step 2: Initialize Razorpay Checkout
      const razorpayOptions: RazorpayOptions = {
        key: 'rzp_test_d7NTm2Fmuuqwld', // Test Mode Key
        amount: orderResponse.order.amount,
        currency: orderResponse.order.currency,
        name: "wHolistic Wellness",
        description: courseTitle,
        image: "/assets/logo.png", // Add your logo
        order_id: orderResponse.order.id,
        handler: async (response: any) => {
          try {
            // Step 3: Verify Payment
            const verificationResponse = await this.verifyPayment({
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
              course_id: courseId,
              delegate_id: delegateId
            });

            if (verificationResponse.success) {
              onSuccess({
                payment_id: response.razorpay_payment_id,
                order_id: response.razorpay_order_id,
                course_id: courseId,
                amount: amount
              });
            } else {
              throw new Error(verificationResponse.error || 'Payment verification failed');
            }
          } catch (error) {
            console.error('Payment verification error:', error);
            onFailure(error);
          }
        },
        prefill: {
          name: delegateName,
          email: delegateEmail || '',
          contact: delegateContact
        },
        notes: {
          course_id: courseId,
          delegate_id: delegateId
        },
        theme: {
          color: "#1e40af" // Your secondary color
        },
        modal: {
          ondismiss: () => {
            onFailure(new Error('Payment cancelled by user'));
          }
        }
      };

      await this.initializePayment(razorpayOptions);
    } catch (error) {
      console.error('Payment process error:', error);
      onFailure(error);
    }
  }
}

export const paymentService = PaymentService.getInstance();
