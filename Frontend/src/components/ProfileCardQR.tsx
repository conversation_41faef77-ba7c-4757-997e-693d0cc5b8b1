import React, { useState, useEffect } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { AiOutlineEdit } from "react-icons/ai";
import useFetchCourses from "../hooks/useFetchCourses";
import { postCourseSelected } from "../hooks/manageDelegates";
import { Button, Card, CardContent, CardHeader, CardTitle, Typography } from './ui';
import { paymentService } from '../services/paymentService';

interface Program {
  course_imagepath?: string;
  course_title?: string;
  course_content?: string;
  course_category?: string;
  scheduledate?: string;
  scheduletime?: string;
  speakername?: string;
  venue?: string;
  coursefees?: number | string;
  courseid?: bigint;
}

interface ProfileCardProps {
  id: number | undefined;
  registrationDate?: string;
  regname?: string;
  ageRange?: string;
  mobileno?: string;
  gender?: string;
  maritalStatus?: string;
  occupation?: string;
  area?: string;
  program?: Program[];
  grade?: string;
  onEdit: () => void;
}

const ProfileCardQR: React.FC<ProfileCardProps> = ({
  registrationDate,
  regname,
  ageRange,
  mobileno,
  gender,
  maritalStatus,
  occupation,
  area,
  program,
  grade,
  onEdit,
}) => {
  const placeholderImage = "/assets/placeholder.png";

  if (!program) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Typography variant="body1" color="neutral">Loading course...</Typography>
      </div>
    );
  }

   const [selectedCards, setSelectedCards] = useState<string[]>([]);
  const navigate = useNavigate();
  const location = useLocation();
  const delegateexist = location.state?.delegateexist;
  const fromQR = location.state?.fromQR;
  const programId = location.state?.programId;
  const delegateId = localStorage.getItem("delegateId") || "";
  const { courses, isLoading, error } = useFetchCourses(delegateId);

useEffect(() => {
    if (courses && courses.length > 0) {
      const initiallySelected = courses
        .filter((course) => course.prevselected === 1)
        .map((course) => course.course_id.toString());
      setSelectedCards(initiallySelected);


    }
  }, [courses]);

  const [isProcessingPayment, setIsProcessingPayment] = useState<string | null>(null);

  const processpayment = async (courseId: string, courseFees: number, courseTitle: string) => {
    try {
      setIsProcessingPayment(courseId);

      // First, add the course to selected courses
      const updatedSelectedCards = Array.from(new Set([...selectedCards, courseId]));
      setSelectedCards(updatedSelectedCards);

      // Process payment with Razorpay
      await paymentService.processPayment({
        amount: courseFees,
        courseId: courseId,
        courseTitle: courseTitle,
        delegateId: delegateId,
        delegateName: regname || 'User',
        delegateContact: mobileno || '',
        delegateEmail: '', // Add email if available
        onSuccess: async (paymentData) => {
          try {
            // Save course selection after successful payment
            await postCourseSelected(delegateId, updatedSelectedCards);

            // Navigate to payment confirmation
            navigate("/paymentconfirmation", {
              state: {
                paymentData,
                courseTitle,
                amount: courseFees
              }
            });
          } catch (error) {
            console.error('Error saving course selection:', error);
            alert("Payment successful but failed to save course selection. Please contact support.");
          } finally {
            setIsProcessingPayment(null);
          }
        },
        onFailure: (error) => {
          console.error('Payment failed:', error);
          alert(`Payment failed: ${error.message || 'Unknown error'}`);
          setIsProcessingPayment(null);
        }
      });
    } catch (error) {
      console.error('Payment process error:', error);
      alert(`Payment initialization failed: ${error.message || 'Unknown error'}`);
      setIsProcessingPayment(null);
    }
  };

const formatDate = (isoString?: string) => {
  if (!isoString) return "";
  const d = new Date(isoString);
  return d.toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" });
};
const formatTime = (timeString?: string) => {
  if (!timeString) return "";
  let dateObj;
  if (timeString.length > 8) {
    dateObj = new Date(timeString);
  } else {
    const today = new Date();
    const [h, m, s] = timeString.split(":");
    dateObj = new Date(today.getFullYear(), today.getMonth(), today.getDate(), Number(h), Number(m), Number(s || 0));
  }
  return dateObj.toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });
};




return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 p-4 md:p-6 border-b border-neutral-200 bg-white">
        <div className="max-w-4xl mx-auto">
          <div className="flex flex-col md:flex-row items-center gap-4 md:gap-6">
            {/* Avatar */}
            <div className="relative flex-shrink-0">
              <div className="h-20 w-20 md:h-24 md:w-24 bg-gradient-to-tr from-secondary-500 to-secondary-700 rounded-full flex items-center justify-center text-white text-2xl md:text-3xl font-bold shadow-lg">
                {regname?.charAt(0).toUpperCase() || "?"}
              </div>
              <button
                onClick={onEdit}
                className="absolute -bottom-1 -right-1 p-2 bg-white rounded-full shadow-md border border-neutral-200 hover:bg-secondary-50 transition-colors"
                aria-label="Edit Profile"
              >
                <AiOutlineEdit size={18} className="text-secondary-600" />
              </button>
            </div>

            {/* Profile Info */}
            <div className="flex-1 min-w-0 text-center md:text-left">
              <Typography variant="h3" weight="bold" className="mb-2 md:mb-3">
                {regname || "Unknown User"}
              </Typography>
              <div className="flex flex-wrap justify-center md:justify-start gap-2 mb-3">
                <span className="bg-neutral-100 text-neutral-600 rounded-lg px-3 py-1 text-sm font-medium">
                  Grade: {grade || "N/A"}
                </span>
                <span className="bg-neutral-100 text-neutral-600 rounded-lg px-3 py-1 text-sm font-medium">
                  Registered: {formatDate(registrationDate) || "N/A"}
                </span>
              </div>
              <div className="flex flex-wrap justify-center md:justify-start gap-2">
                {ageRange && (
                  <span className="bg-secondary-50 text-secondary-700 rounded-lg px-2 py-1 text-sm">
                    Age: {ageRange}
                  </span>
                )}
                {gender && (
                  <span className="bg-secondary-50 text-secondary-700 rounded-lg px-2 py-1 text-sm">
                    {gender}
                  </span>
                )}
                {mobileno && (
                  <span className="bg-secondary-50 text-secondary-700 rounded-lg px-2 py-1 text-sm">
                    {mobileno}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1 p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          <Typography variant="h4" weight="bold" color="primary" className="flex items-center gap-2 mb-6 justify-center md:justify-start">
            <span>📋</span>
            Program Details
          </Typography>

          <div className="grid gap-6 md:gap-8">
            {program.map((item, index) => (
              <Card key={index} variant="default" className="overflow-hidden">
                <CardContent className="p-4 md:p-6">
                  <div className="flex flex-col md:flex-row gap-4 md:gap-6 mb-6">
                    <img
                      src={item.course_imagepath ? `/assets/${item.course_imagepath}` : "/assets/placeholder.png"}
                      alt={item.course_title}
                      className="rounded-lg h-32 w-32 md:h-40 md:w-40 object-cover border border-neutral-200 mx-auto md:mx-0 flex-shrink-0"
                    />
                    <div className="flex-1 min-w-0 text-center md:text-left">
                      <div className="flex flex-wrap justify-center md:justify-start gap-2 mb-3">
                        {item.course_category && (
                          <span className="bg-accent-100 text-accent-800 font-semibold rounded-lg px-3 py-1 text-sm uppercase tracking-wide">
                            {item.course_category}
                          </span>
                        )}
                        <span className="bg-secondary-100 text-secondary-800 rounded-lg px-3 py-1 text-sm font-semibold">
                          ID: {item.courseid?.toString().slice(-4)}
                        </span>
                      </div>
                      <Typography variant="h4" weight="bold" color="primary" className="mb-3 md:text-2xl">
                        {item.course_title}
                      </Typography>
                      <Typography variant="body1" color="neutral" className="leading-relaxed">
                        {item.course_content}
                      </Typography>
                    </div>
                  </div>

                  {/* Course Details Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 text-neutral-600">
                    <div className="flex items-center justify-center md:justify-start gap-3">
                      <span className="text-lg">📅</span>
                      <span className="font-medium">{formatDate(item.scheduledate)}</span>
                    </div>
                    <div className="flex items-center justify-center md:justify-start gap-3">
                      <span className="text-lg">⏰</span>
                      <span className="font-medium">{formatTime(item.scheduletime)}</span>
                    </div>
                    <div className="flex items-center justify-center md:justify-start gap-3 md:col-span-2">
                      <span className="text-lg">📍</span>
                      <span className="font-medium">{item.venue}</span>
                    </div>
                    <div className="flex items-center justify-center md:justify-start gap-3 md:col-span-2">
                      <span className="text-lg">🧑‍💼</span>
                      <span className="font-medium">{item.speakername}</span>
                    </div>
                  </div>

                  {/* Price and Payment */}
                  <div className="flex flex-col md:flex-row items-center justify-center md:justify-between gap-4">
                    <div className="bg-gradient-to-r from-secondary-600 to-secondary-700 text-white font-bold rounded-lg px-6 py-3 shadow-md text-lg">
                      ₹ {item.coursefees}
                    </div>
                    <Button
                      onClick={() => processpayment(
                        item.courseid?.toString() || "",
                        Number(item.coursefees) || 0,
                        item.course_title || "Course"
                      )}
                      variant="accent"
                      size="lg"
                      className="w-full md:w-auto md:min-w-40"
                      disabled={isProcessingPayment === item.courseid?.toString()}
                    >
                      {isProcessingPayment === item.courseid?.toString() ? (
                        <>
                          <svg className="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"/>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                          </svg>
                          Processing...
                        </>
                      ) : (
                        <>💳 Pay Now</>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

};

export default ProfileCardQR;
