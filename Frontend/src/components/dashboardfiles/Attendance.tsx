import React, { useState, useEffect } from "react";
import { fetchAllPrograms, cancelProgramDemo, updateDelegateStatus } from "../../hooks/managePrograms"; // Importing API calls
import useFetchAllCourses from '../../hooks/useFetchAllCourses';
import useFetchProgramDelegates from '../../hooks/useFetchProgramDelegates'; // Import the new hook

interface Program {
  id?: string; // Make id optional
  courseid: number;
  scheduledate: string;
  venue: string;
  speakername: string;
  organizer: string;
  organizercontact1: number;
  organizercontact2: number;
}

interface ProgramDelegate {
  programid: number;
  id: number;
  registrationdate: string;
  regname: string;
  agerange: string;
  mobileno: string;
  gender: string;
  maritalstatus: string;
  occupation: string;
  area: string;
  dateofbirth: string;
  delegategrade: string;
  iscontacted: boolean;
  isconfirmed: boolean;
  ispaymentreceived: boolean;
  iscancelled: boolean;
  isattended: boolean;
}

const AttendanceSheet: React.FC = () => {
  const [programs, setPrograms] = useState<Program[]>([]);
  const [selectedProgramId, setSelectedProgramId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { courses, isLoadingac, errorac } = useFetchAllCourses();
  const { data: delegates, loading: delegatesLoading, error: delegatesError, fetchDelegates } = useFetchProgramDelegates(selectedProgramId || 0);

  const formatDate = (date: string) => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Fetch all programs on component mount
  useEffect(() => {
    const fetchPrograms = async () => {
      setIsLoading(true);
      try {
        const data: Program[] = await fetchAllPrograms(); // Explicit type assertion
        setPrograms(data);
      } catch (error) {
        console.error("Error fetching programs:", error);
        setError("Failed to fetch programs.");
      } finally {
        setIsLoading(false);
      }
    };
    fetchPrograms();
  }, []);

  // Fetch delegates when a program is selected
  useEffect(() => {
    if (selectedProgramId) {
      fetchDelegates(selectedProgramId);
    }
  }, [selectedProgramId]);

  
  const handleDelegateStatusChange = async (
    delegateId: number,
    programId: number,
    field: keyof ProgramDelegate,
    value: boolean
  ) => {
    try {
      // Call the API to update the delegate status
      await updateDelegateStatus({
        delegateid: delegateId, // Corrected parameter name
        programid: programId,
        [field]: value,
      });
  
      // Show success message
      alert("Delegate status updated successfully!");
  
      // Refresh the delegates list
      if (selectedProgramId) {
        fetchDelegates(selectedProgramId);
      }
    } catch (error) {
      console.error("Error updating delegate status:", error);
  
      // Display error message in the UI
      setError("Failed to update delegate status. Please try again.");
  
      // Optionally, you can show a more specific error message
      if (error instanceof Error) {
        setError(`Error: ${error.message}`);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-4 lg:p-8">
      {/* Header Section */}
      <div className="max-w-7xl mx-auto mb-8">
        <div className="text-center">
          <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
            📋 Attendance Management
          </h1>
          <p className="text-gray-600 text-lg">
            Track and manage participant attendance for wellness programs
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto grid grid-cols-1 xl:grid-cols-2 gap-8">
        {/* Programs List */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
            <h3 className="text-xl font-semibold text-white flex items-center gap-2">
              📚 Select Program
              <span className="bg-white/20 text-white text-sm px-2 py-1 rounded-full">
                {programs.length}
              </span>
            </h3>
          </div>

          <div className="p-6">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
                <span className="ml-4 text-gray-600 font-medium">Loading programs...</span>
              </div>
            ) : programs.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📝</div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">No Programs Available</h4>
                <p className="text-gray-600">Create programs to manage attendance</p>
              </div>
            ) : (
              <div className="space-y-4 max-h-[600px] overflow-y-auto">
                {programs.map((program) => (
                  <div
                    key={program.id}
                    className={`rounded-xl p-6 border-2 transition-all duration-200 cursor-pointer ${
                      selectedProgramId === parseInt(program.id || '0')
                        ? 'border-blue-500 bg-blue-50 shadow-lg'
                        : 'border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50 hover:border-blue-300 hover:shadow-md'
                    }`}
                    onClick={() => setSelectedProgramId(parseInt(program.id || '0'))}
                  >
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                      <div className="flex-1 space-y-3">
                        <div className="flex items-center gap-2">
                          <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-3 py-1 rounded-full">
                            {courses.find(c => c.course_id === program.courseid)?.course_title || 'Unknown Course'}
                          </span>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                          <div className="flex items-center gap-2 text-gray-700">
                            <span className="text-blue-500">📅</span>
                            <span className="font-medium">Date:</span>
                            <span>{formatDate(program.scheduledate)}</span>
                          </div>
                          <div className="flex items-center gap-2 text-gray-700">
                            <span className="text-purple-500">📍</span>
                            <span className="font-medium">Venue:</span>
                            <span>{program.venue}</span>
                          </div>
                          <div className="flex items-center gap-2 text-gray-700">
                            <span className="text-orange-500">🎤</span>
                            <span className="font-medium">Speaker:</span>
                            <span>{program.speakername}</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center">
                        {selectedProgramId === parseInt(program.id || '0') ? (
                          <div className="flex items-center gap-2 text-blue-600 font-medium">
                            <span className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
                            Selected
                          </div>
                        ) : (
                          <button className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 flex items-center gap-2 text-sm font-medium">
                            👥 View Attendance
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Delegates/Attendance Management */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
            <h3 className="text-xl font-semibold text-white flex items-center gap-2">
              👥 Participant Management
              {selectedProgramId && delegates.length > 0 && (
                <span className="bg-white/20 text-white text-sm px-2 py-1 rounded-full">
                  {delegates.length} participants
                </span>
              )}
            </h3>
          </div>

          <div className="p-6">
            {selectedProgramId ? (
              <div>
                {delegatesLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-4 border-indigo-500 border-t-transparent"></div>
                    <span className="ml-4 text-gray-600 font-medium">Loading participants...</span>
                  </div>
                ) : delegatesError ? (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">⚠️</div>
                    <h4 className="text-lg font-semibold text-red-600 mb-2">Error Loading Data</h4>
                    <p className="text-gray-600">{delegatesError}</p>
                  </div>
                ) : delegates.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">👥</div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">No Participants Found</h4>
                    <p className="text-gray-600">No one has registered for this program yet</p>
                  </div>
                ) : (
                  <div>
                    {/* Summary Cards */}
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                      <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-900">{delegates.length}</div>
                          <div className="text-sm font-medium text-blue-700">Total</div>
                        </div>
                      </div>
                      <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-4 border border-green-200">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-900">
                            {delegates.filter(d => d.isconfirmed).length}
                          </div>
                          <div className="text-sm font-medium text-green-700">Confirmed</div>
                        </div>
                      </div>
                      <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-4 border border-purple-200">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-900">
                            {delegates.filter(d => d.ispaymentreceived).length}
                          </div>
                          <div className="text-sm font-medium text-purple-700">Paid</div>
                        </div>
                      </div>
                      <div className="bg-gradient-to-r from-emerald-50 to-emerald-100 rounded-xl p-4 border border-emerald-200">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-emerald-900">
                            {delegates.filter(d => d.isattended).length}
                          </div>
                          <div className="text-sm font-medium text-emerald-700">Attended</div>
                        </div>
                      </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="flex flex-wrap gap-3 mb-6">
                      <button
                        onClick={() => {
                          delegates.forEach(delegate => {
                            if (!delegate.isconfirmed) {
                              handleDelegateStatusChange(delegate.id, delegate.programid, 'isconfirmed', true);
                            }
                          });
                        }}
                        className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 flex items-center gap-2 text-sm font-medium"
                      >
                        ✅ Confirm All
                      </button>
                      <button
                        onClick={() => {
                          delegates.forEach(delegate => {
                            if (!delegate.isattended) {
                              handleDelegateStatusChange(delegate.id, delegate.programid, 'isattended', true);
                            }
                          });
                        }}
                        className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200 flex items-center gap-2 text-sm font-medium"
                      >
                        👥 Mark All Present
                      </button>
                      <button
                        onClick={() => {
                          delegates.forEach(delegate => {
                            if (delegate.isattended) {
                              handleDelegateStatusChange(delegate.id, delegate.programid, 'isattended', false);
                            }
                          });
                        }}
                        className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200 flex items-center gap-2 text-sm font-medium"
                      >
                        ❌ Mark All Absent
                      </button>
                    </div>

                    {/* Participants Table */}
                    <div className="bg-gray-50 rounded-xl overflow-hidden">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead className="bg-gray-100">
                            <tr>
                              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Participant</th>
                              <th className="px-4 py-4 text-center text-sm font-semibold text-gray-900">Confirmed</th>
                              <th className="px-4 py-4 text-center text-sm font-semibold text-gray-900">Payment</th>
                              <th className="px-4 py-4 text-center text-sm font-semibold text-gray-900">Cancelled</th>
                              <th className="px-4 py-4 text-center text-sm font-semibold text-gray-900">Attended</th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200">
                            {delegates.map((delegate, index) => (
                              <tr key={delegate.id} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-blue-50 transition-colors duration-150`}>
                                <td className="px-6 py-4">
                                  <div className="flex items-center gap-3">
                                    <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold ${
                                      delegate.isattended ? 'bg-green-500' :
                                      delegate.isconfirmed ? 'bg-blue-500' : 'bg-gray-400'
                                    }`}>
                                      {delegate.regname.charAt(0).toUpperCase()}
                                    </div>
                                    <div>
                                      <div className="font-semibold text-gray-900">{delegate.regname}</div>
                                      <div className="text-sm text-gray-500">ID: {delegate.id}</div>
                                    </div>
                                  </div>
                                </td>
                                <td className="px-4 py-4 text-center">
                                  <label className="inline-flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={delegate.isconfirmed}
                                      onChange={(e) => handleDelegateStatusChange(delegate.id, delegate.programid, 'isconfirmed', e.target.checked)}
                                      className="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                                    />
                                  </label>
                                </td>
                                <td className="px-4 py-4 text-center">
                                  <label className="inline-flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={delegate.ispaymentreceived}
                                      onChange={(e) => handleDelegateStatusChange(delegate.id, delegate.programid, 'ispaymentreceived', e.target.checked)}
                                      className="w-5 h-5 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"
                                    />
                                  </label>
                                </td>
                                <td className="px-4 py-4 text-center">
                                  <label className="inline-flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={delegate.iscancelled}
                                      onChange={(e) => handleDelegateStatusChange(delegate.id, delegate.programid, 'iscancelled', e.target.checked)}
                                      className="w-5 h-5 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500 focus:ring-2"
                                    />
                                  </label>
                                </td>
                                <td className="px-4 py-4 text-center">
                                  <label className="inline-flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={delegate.isattended}
                                      onChange={(e) => handleDelegateStatusChange(delegate.id, delegate.programid, 'isattended', e.target.checked)}
                                      className="w-5 h-5 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                                    />
                                  </label>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">👈</div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Select a Program</h4>
                <p className="text-gray-600">Choose a program from the left to manage participant attendance</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AttendanceSheet;