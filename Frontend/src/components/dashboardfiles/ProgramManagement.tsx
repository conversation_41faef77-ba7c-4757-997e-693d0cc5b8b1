import React, { useState, useEffect } from "react";
import {
  postProgramDemo,
  fetchAllPrograms,
  updateProgramDemo,
  cancelProgramDemo,
} from "../../hooks/managePrograms";
import useFetchAllCourses from '../../hooks/useFetchAllCourses';
import useFetchCourseParticipants from '../../hooks/useFetchCourseParticipants';
import { BASE_URL } from '../../config';
import { Calendar, dateFnsLocalizer } from 'react-big-calendar';
import { format, parse, startOfWeek, getDay } from 'date-fns';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { enUS } from 'date-fns/locale/en-US';

const locales = {
  'en-US': enUS,
};

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales,
});
 
// Updated interfaces with new fields
interface Program {
  id?: string;
  courseid: number;
  scheduledate: string;
  scheduletime?: string; // new field: time portion
  venue: string;
  speakername: string;
  organizer: string;
  organizercontact1: number;
  organizercontact2: number;
  coursefees?: number; // new field for course fees
  qr_code_url?: string; // QR code URL field
}
 
interface ProgramFormValues extends Program {}
 
interface Participant {
  id: bigint;
  delegateid: number;
  registrationdate: string;
  regname: string;
  agerange: string;
  mobileno: string;
  gender: string;
  maritalstatus: string;
  occupation: string;
  area: string;
  grade: string;
}
 
const ProgramManagement: React.FC = () => {
  // Updated initial state with new fields
  const [formData, setFormData] = useState<ProgramFormValues>({
    courseid: 0,
    scheduledate: "",
    scheduletime: "",
    venue: "",
    speakername: "",
    organizer: "",
    organizercontact1: 0,
    organizercontact2: 0,
    coursefees: 0,
  });
  const [programs, setPrograms] = useState<Program[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedCourseId, setSelectedCourseId] = useState<number | null>(null);
  const [isSideWindowOpen, setIsSideWindowOpen] = useState(false);
  const [showCalendar, setShowCalendar] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Program | null>(null);
  const [showQRModal, setShowQRModal] = useState(false);
  const [selectedProgramForQR, setSelectedProgramForQR] = useState<Program | null>(null);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [isGeneratingQR, setIsGeneratingQR] = useState(false);

  const { courses, isLoadingac, errorac, refetchCourses } = useFetchAllCourses();
  const {
    data: participants,
    loading: participantsLoading,
    error: participantsError,
    fetchParticipants,
  } = useFetchCourseParticipants(selectedCourseId);
 
  // Updated calendar events (if needed you can combine date & time)
  const calendarEvents = programs.map(program => {
    // Optionally combine date and time to construct a proper Date for calendar events:
    const eventDateTime = program.scheduletime
      ? new Date(`${program.scheduledate}T${program.scheduletime}`)
      : new Date(program.scheduledate);
      
    return {
      title: courses.find(c => c.course_id === program.courseid)?.course_title || 'Unknown Course',
      start: eventDateTime,
      end: eventDateTime, // If duration is needed, you might add time to this value.
      allDay: !program.scheduletime, // if no specific time is provided, treat as all day.
      resource: program,
    };
  });
 
  // Helper to format date (you might want to also show time separately if required)
  const formatDate = (date: string) => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
 
  useEffect(() => {
    const fetchPrograms = async () => {
      try {
        const data: Program[] = await fetchAllPrograms();
        setPrograms(data);
      } catch (error) {
        console.error("Error fetching programs:", error);
        setError("Failed to fetch programs.");
      }
    };
    fetchPrograms();
  }, []);
 
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: (name === "courseid" || name === "coursefees")
        ? Number(value)
        : value,
    }));
 
    if (name === "courseid") {
      const courseId = Number(value);
      setSelectedCourseId(courseId);
      handleParticipantsClick(courseId);
    }
  };
 
  const validateDate = (date: string): boolean => {
    const selectedDate = new Date(date);
    const today = new Date();
    return selectedDate >= today;
  };
 
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
 
    if (!validateDate(formData.scheduledate)) {
      alert("Please select a future date for the program.");
      setIsLoading(false);
      return;
    }
 
    try {
      if (formData.id) {
        await updateProgramDemo(formData.id, {
          ...formData,
          organizercontact1: formData.organizercontact1,
          organizercontact2: formData.organizercontact2,
          coursefees: formData.coursefees,
          scheduletime: formData.scheduletime,
        });
        alert("Program updated successfully!");
      } else {
        await postProgramDemo({
          ...formData,
          organizercontact1: formData.organizercontact1,
          organizercontact2: formData.organizercontact2,
          coursefees: formData.coursefees,
          scheduletime: formData.scheduletime,
          addedby: 1,
          unitid: 1,
        });
        alert("Program created successfully!");
      }
 
      const data: Program[] = await fetchAllPrograms();
      setPrograms(data);
      // Reset form with new fields included
      setFormData({
        courseid: 0,
        scheduledate: "",
        scheduletime: "",
        venue: "",
        speakername: "",
        organizer: "",
        organizercontact1: 0,
        organizercontact2: 0,
        coursefees: 0,
      });
    } catch (error) {
      console.error("Error:", error);
      setError("There was an error during the operation. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };
 
  const handleEdit = (program: Program) => {
    setFormData({
      id: program.id,
      courseid: Number(program.courseid),
      scheduledate: formatDate(program.scheduledate),
      scheduletime: program.scheduletime || "",
      venue: program.venue,
      speakername: program.speakername,
      organizer: program.organizer,
      organizercontact1: program.organizercontact1,
      organizercontact2: program.organizercontact2,
      coursefees: program.coursefees || 0,
    });
  };
 
  const handleCancel = async (programId: string) => {
    if (window.confirm("Are you sure you want to cancel this program?")) {
      try {
        await cancelProgramDemo(programId, 1);
        alert("Program canceled successfully!");
        const data = await fetchAllPrograms();
        setPrograms(data);
      } catch (error) {
        console.error("Error:", error);
        setError("Failed to cancel the program.");
      }
    }
  };
 
  const handleParticipantsClick = async (courseId: number) => {
    setSelectedCourseId(courseId);
    await fetchParticipants(courseId);
    setIsSideWindowOpen(true);
  };
 
  const closeSideWindow = () => {
    setIsSideWindowOpen(false);
    setSelectedCourseId(null);
  };

  const handleGenerateQR = async (program: Program) => {
    if (!program.id) return;

    setSelectedProgramForQR(program);
    setIsGeneratingQR(true);
    setShowQRModal(true);

    try {
      // First check if QR code already exists
      if (program.qr_code_url) {
        const qrUrl = `${BASE_URL}${program.qr_code_url}`;
        console.log('Using existing QR code URL:', qrUrl);
        setQrCodeUrl(qrUrl);
        setIsGeneratingQR(false);
        return;
      }

      // Generate new QR code
      const response = await fetch(`${BASE_URL}/programs/generateqrcode`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ programId: program.id }),
      });

      const data = await response.json();
      console.log('QR generation response:', data);

      if (data.success) {
        const qrUrl = `${BASE_URL}${data.qrCodeUrl}`;
        console.log('Generated QR code URL:', qrUrl);
        setQrCodeUrl(qrUrl);
        // Update the program in the local state
        setPrograms(prev => prev.map(p =>
          p.id === program.id ? { ...p, qr_code_url: data.qrCodeUrl } : p
        ));
      } else {
        console.error('QR generation failed:', data);
        alert('Failed to generate QR code');
      }
    } catch (error) {
      console.error('Error generating QR code:', error);
      alert('Error generating QR code');
    } finally {
      setIsGeneratingQR(false);
    }
  };

  const handleDownloadQR = () => {
    if (!qrCodeUrl || !selectedProgramForQR) return;

    const link = document.createElement('a');
    link.href = qrCodeUrl;
    link.download = `program_${selectedProgramForQR.id}_qr.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const closeQRModal = () => {
    setShowQRModal(false);
    setSelectedProgramForQR(null);
    setQrCodeUrl('');
  };
 
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-4 lg:p-8">
      {/* Header Section */}
      <div className="max-w-7xl mx-auto mb-8">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
              Program Management
            </h1>
            <p className="text-gray-600 text-lg">
              Create, manage, and track wellness programs
            </p>
          </div>

          {/* View Toggle */}
          <div className="flex items-center bg-white rounded-xl shadow-sm border border-gray-200 p-1">
            <button
              onClick={() => setShowCalendar(false)}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                !showCalendar
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              📋 List View
            </button>
            <button
              onClick={() => setShowCalendar(true)}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                showCalendar
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              📅 Calendar View
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto grid grid-cols-1 xl:grid-cols-2 gap-8">
        {/* Create Program Form */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
            <h2 className="text-xl font-semibold text-white flex items-center gap-2">
              {formData.id ? "✏️ Edit Program" : "➕ Create New Program"}
            </h2>
          </div>

          <div className="p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Course Selection */}
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700 flex items-center gap-2">
                  🎓 Course
                </label>
                <select
                  name="courseid"
                  value={formData.courseid}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white"
                  required
                >
                  <option value="">Select a course...</option>
                  {courses.map((course) => (
                    <option key={course.course_id} value={course.course_id}>
                      {course.course_title}
                    </option>
                  ))}
                </select>
              </div>

              {/* Date and Time Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700 flex items-center gap-2">
                    📅 Scheduled Date
                  </label>
                  <input
                    type="date"
                    name="scheduledate"
                    value={formData.scheduledate}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700 flex items-center gap-2">
                    ⏰ Scheduled Time
                  </label>
                  <input
                    type="time"
                    name="scheduletime"
                    value={formData.scheduletime}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white"
                  />
                </div>
              </div>

              {/* Venue and Speaker Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700 flex items-center gap-2">
                    📍 Venue
                  </label>
                  <input
                    type="text"
                    name="venue"
                    value={formData.venue}
                    onChange={handleChange}
                    placeholder="Enter venue location..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700 flex items-center gap-2">
                    🎤 Speaker Name
                  </label>
                  <input
                    type="text"
                    name="speakername"
                    value={formData.speakername}
                    onChange={handleChange}
                    placeholder="Enter speaker name..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white"
                    required
                  />
                </div>
              </div>

              {/* Organizer */}
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700 flex items-center gap-2">
                  👤 Organizer
                </label>
                <input
                  type="text"
                  name="organizer"
                  value={formData.organizer}
                  onChange={handleChange}
                  placeholder="Enter organizer name..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white"
                  required
                />
              </div>
              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700 flex items-center gap-2">
                    📞 Primary Contact
                  </label>
                  <input
                    type="tel"
                    name="organizercontact1"
                    value={formData.organizercontact1}
                    onChange={handleChange}
                    placeholder="Enter primary contact..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700 flex items-center gap-2">
                    📱 Secondary Contact
                  </label>
                  <input
                    type="tel"
                    name="organizercontact2"
                    value={formData.organizercontact2}
                    onChange={handleChange}
                    placeholder="Enter secondary contact..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white"
                    required
                  />
                </div>
              </div>

              {/* Course Fees */}
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700 flex items-center gap-2">
                  💰 Course Fees (₹)
                </label>
                <input
                  type="number"
                  step="0.01"
                  name="coursefees"
                  value={formData.coursefees}
                  onChange={handleChange}
                  placeholder="Enter course fees..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white"
                />
              </div>

              {/* Submit Button */}
              <div className="pt-4">
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold py-4 px-6 rounded-xl hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center gap-2">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      Processing...
                    </div>
                  ) : (
                    <span className="flex items-center justify-center gap-2">
                      {formData.id ? "✏️ Update Program" : "➕ Create Program"}
                    </span>
                  )}
                </button>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                  <p className="text-red-600 text-sm flex items-center gap-2">
                    ⚠️ {error}
                  </p>
                </div>
              )}
            </form>
          </div>
        </div>
        {/* Right Side (Calendar or Program List) */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          {showCalendar ? (
            <div>
              <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
                <h3 className="text-xl font-semibold text-white flex items-center gap-2">
                  📅 Program Calendar
                </h3>
              </div>
              <div className="p-6">
                <div className="h-[600px] bg-gray-50 rounded-xl overflow-hidden">
                  <Calendar
                    localizer={localizer}
                    events={calendarEvents}
                    startAccessor="start"
                    endAccessor="end"
                    onSelectEvent={(event: any) => setSelectedEvent(event.resource)}
                    className="h-full"
                  />
                </div>
              </div>
            </div>
          ) : (
            <div>
              <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
                <h3 className="text-xl font-semibold text-white flex items-center gap-2">
                  📋 Programs List
                  <span className="bg-white/20 text-white text-sm px-2 py-1 rounded-full">
                    {programs.length}
                  </span>
                </h3>
              </div>

              <div className="p-6">
                {programs.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">📝</div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">No Programs Yet</h4>
                    <p className="text-gray-600">Create your first program to get started!</p>
                  </div>
                ) : (
                  <div className="space-y-4 max-h-[600px] overflow-y-auto">
                    {programs.map((program) => (
                      <div key={program.id} className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-200 hover:border-blue-300">
                        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                          <div className="flex-1 space-y-3">
                            <div className="flex items-center gap-2">
                              <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded-full">
                                {courses.find(c => c.course_id === program.courseid)?.course_title || 'Unknown Course'}
                              </span>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                              <div className="flex items-center gap-2 text-gray-700">
                                <span className="text-blue-500">📅</span>
                                <span className="font-medium">Date:</span>
                                <span>{formatDate(program.scheduledate)}</span>
                              </div>
                              <div className="flex items-center gap-2 text-gray-700">
                                <span className="text-blue-500">⏰</span>
                                <span className="font-medium">Time:</span>
                                <span>{program.scheduletime || "Not specified"}</span>
                              </div>
                              <div className="flex items-center gap-2 text-gray-700">
                                <span className="text-blue-500">📍</span>
                                <span className="font-medium">Venue:</span>
                                <span>{program.venue}</span>
                              </div>
                              <div className="flex items-center gap-2 text-gray-700">
                                <span className="text-blue-500">🎤</span>
                                <span className="font-medium">Speaker:</span>
                                <span>{program.speakername}</span>
                              </div>
                              <div className="flex items-center gap-2 text-gray-700">
                                <span className="text-blue-500">💰</span>
                                <span className="font-medium">Fees:</span>
                                <span className="font-semibold text-green-600">
                                  ₹{program.coursefees ? Number(program.coursefees).toFixed(2) : "0.00"}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="flex flex-wrap gap-2">
                            <button
                              onClick={() => handleEdit(program)}
                              className="px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors duration-200 flex items-center gap-2 text-sm font-medium"
                              title="Edit Program"
                            >
                              ✏️ Edit
                            </button>
                            <button
                              onClick={() => handleGenerateQR(program)}
                              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 flex items-center gap-2 text-sm font-medium"
                              title="Generate QR Code"
                            >
                              📱 QR
                            </button>
                            <button
                              onClick={() => program.id && handleCancel(program.id)}
                              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200 flex items-center gap-2 text-sm font-medium"
                              title="Cancel Program"
                            >
                              ❌ Cancel
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
 
      {/* Event Details Modal */}
      {selectedEvent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4 rounded-t-2xl">
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-semibold text-white flex items-center gap-2">
                  📋 Program Details
                </h3>
                <button
                  onClick={() => setSelectedEvent(null)}
                  className="text-white hover:text-gray-200 text-2xl font-bold transition-colors duration-200"
                >
                  ×
                </button>
              </div>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-blue-50 rounded-xl p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-blue-500">🎓</span>
                      <span className="font-semibold text-gray-700">Course</span>
                    </div>
                    <p className="text-gray-900 font-medium">
                      {courses.find(c => c.course_id === selectedEvent.courseid)?.course_title}
                    </p>
                  </div>

                  <div className="bg-green-50 rounded-xl p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-green-500">📅</span>
                      <span className="font-semibold text-gray-700">Date & Time</span>
                    </div>
                    <p className="text-gray-900 font-medium">
                      {formatDate(selectedEvent.scheduledate)}
                      {selectedEvent.scheduletime && ` at ${selectedEvent.scheduletime}`}
                    </p>
                  </div>

                  <div className="bg-purple-50 rounded-xl p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-purple-500">📍</span>
                      <span className="font-semibold text-gray-700">Venue</span>
                    </div>
                    <p className="text-gray-900 font-medium">{selectedEvent.venue}</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-orange-50 rounded-xl p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-orange-500">🎤</span>
                      <span className="font-semibold text-gray-700">Speaker</span>
                    </div>
                    <p className="text-gray-900 font-medium">{selectedEvent.speakername}</p>
                  </div>

                  <div className="bg-indigo-50 rounded-xl p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-indigo-500">👤</span>
                      <span className="font-semibold text-gray-700">Organizer</span>
                    </div>
                    <p className="text-gray-900 font-medium">{selectedEvent.organizer}</p>
                  </div>

                  <div className="bg-emerald-50 rounded-xl p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-emerald-500">💰</span>
                      <span className="font-semibold text-gray-700">Course Fees</span>
                    </div>
                    <p className="text-gray-900 font-bold text-lg">
                      ₹{!isNaN(Number(selectedEvent.coursefees)) ? Number(selectedEvent.coursefees).toFixed(2) : 'N/A'}
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex flex-wrap gap-3 mt-8 pt-6 border-t border-gray-200">
                <button
                  onClick={() => {
                    handleEdit(selectedEvent);
                    setSelectedEvent(null);
                  }}
                  className="px-6 py-3 bg-amber-500 text-white rounded-xl hover:bg-amber-600 transition-colors duration-200 flex items-center gap-2 font-medium"
                >
                  ✏️ Edit Program
                </button>
                <button
                  onClick={() => {
                    handleGenerateQR(selectedEvent);
                    setSelectedEvent(null);
                  }}
                  className="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors duration-200 flex items-center gap-2 font-medium"
                >
                  📱 Generate QR
                </button>
                <button
                  onClick={() => {
                    selectedEvent.id && handleCancel(selectedEvent.id);
                    setSelectedEvent(null);
                  }}
                  className="px-6 py-3 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors duration-200 flex items-center gap-2 font-medium"
                >
                  ❌ Cancel Program
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
 
      {/* Participants Window */}
      {isSideWindowOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-end">
          <div className="bg-white w-full md:w-3/4 lg:w-2/3 p-6 overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Participants List</h2>
              <button
                className="text-gray-600 hover:text-gray-800"
                onClick={closeSideWindow}
                aria-label="Close participants window"
              >
                &times;
              </button>
            </div>
            {participantsLoading && (
              <div className="text-center">Loading participants...</div>
            )}
            {participantsError && (
              <div className="text-center text-red-500">
                Error loading participants
              </div>
            )}
            {!participantsLoading &&
              !participantsError &&
              participants.length === 0 && (
                <div className="text-center text-gray-500">
                  No participants found for this course.
                </div>
              )}
            {!participantsLoading &&
              !participantsError &&
              participants.length > 0 && (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="p-2 border min-w-[100px]">ID</th>
                        <th className="p-2 border min-w-[150px]">Registration Date</th>
                        <th className="p-2 border min-w-[200px]">Name</th>
                        <th className="p-2 border min-w-[100px]">Age Range</th>
                        <th className="p-2 border min-w-[150px]">Mobile No</th>
                        <th className="p-2 border min-w-[100px]">Gender</th>
                        <th className="p-2 border min-w-[150px]">Marital Status</th>
                        <th className="p-2 border min-w-[150px]">Occupation</th>
                        <th className="p-2 border min-w-[150px]">Area</th>
                        <th className="p-2 border min-w-[100px]">Grade</th>
                      </tr>
                    </thead>
                    <tbody>
                      {participants.map((participant) => (
                        <tr key={participant.id} className="border">
                          <td className="p-2 border">{participant.delegateid}</td>
                          <td className="p-2 border">{participant.registrationdate}</td>
                          <td className="p-2 border">{participant.regname}</td>
                          <td className="p-2 border">{participant.agerange}</td>
                          <td className="p-2 border">{participant.mobileno}</td>
                          <td className="p-2 border">{participant.gender}</td>
                          <td className="p-2 border">{participant.maritalstatus}</td>
                          <td className="p-2 border">{participant.occupation}</td>
                          <td className="p-2 border">{participant.area}</td>
                          <td className="p-2 border">{participant.grade}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
          </div>
        </div>
      )}

      {/* QR Code Modal */}
      {showQRModal && selectedProgramForQR && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-lg">
            <div className="bg-gradient-to-r from-purple-600 to-pink-600 px-6 py-4 rounded-t-2xl">
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-semibold text-white flex items-center gap-2">
                  📱 Program QR Code
                </h3>
                <button
                  onClick={closeQRModal}
                  className="text-white hover:text-gray-200 text-2xl font-bold transition-colors duration-200"
                >
                  ×
                </button>
              </div>
            </div>

            <div className="p-6">
              {/* Program Info */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-6">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="text-blue-500">🎓</span>
                    <span className="font-semibold text-gray-700">Course:</span>
                    <span className="text-gray-900">
                      {courses.find(c => c.course_id === selectedProgramForQR.courseid)?.course_title}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-green-500">📅</span>
                    <span className="font-semibold text-gray-700">Date:</span>
                    <span className="text-gray-900">{formatDate(selectedProgramForQR.scheduledate)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-purple-500">📍</span>
                    <span className="font-semibold text-gray-700">Venue:</span>
                    <span className="text-gray-900">{selectedProgramForQR.venue}</span>
                  </div>
                </div>
              </div>

              {/* QR Code Section */}
              <div className="text-center">
                {isGeneratingQR ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-4 border-purple-500 border-t-transparent mb-4"></div>
                    <p className="text-gray-600 font-medium">Generating QR Code...</p>
                  </div>
                ) : qrCodeUrl ? (
                  <div>
                    <div className="bg-white p-6 rounded-2xl shadow-inner border-2 border-gray-100 mb-6 inline-block">
                      <img
                        src={qrCodeUrl}
                        alt="Program QR Code"
                        className="mx-auto rounded-xl"
                        style={{ width: '240px', height: '240px' }}
                        onLoad={() => console.log('QR code image loaded successfully')}
                        onError={(e) => {
                          console.error('QR code image failed to load:', e);
                          console.error('Failed URL:', qrCodeUrl);
                        }}
                      />
                    </div>

                    <div className="bg-gray-50 rounded-xl p-4 mb-6">
                      <p className="text-sm text-gray-600 flex items-center justify-center gap-2">
                        📱 <span>Scan this QR code to access the program registration page</span>
                      </p>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-3">
                      <button
                        onClick={handleDownloadQR}
                        className="flex-1 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex items-center justify-center gap-2 font-medium shadow-lg hover:shadow-xl"
                      >
                        📥 Download QR Code
                      </button>
                      <button
                        onClick={() => {
                          const programUrl = `${window.location.origin}/wellness/course-info/${selectedProgramForQR.id}`;
                          navigator.clipboard.writeText(programUrl);
                          alert('Program URL copied to clipboard!');
                        }}
                        className="flex-1 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 flex items-center justify-center gap-2 font-medium shadow-lg hover:shadow-xl"
                      >
                        📋 Copy URL
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="py-12">
                    <div className="text-6xl mb-4">❌</div>
                    <p className="text-red-600 font-medium">Failed to generate QR code</p>
                    <p className="text-gray-500 text-sm mt-2">Please try again</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
 
export default ProgramManagement;
