import React, { useState, useEffect } from "react";
import {
  postProgramDemo,
  fetchAllPrograms,
  updateProgramDemo,
  cancelProgramDemo,
} from "../../hooks/managePrograms";
import useFetchAllCourses from '../../hooks/useFetchAllCourses';
import useFetchCourseParticipants from '../../hooks/useFetchCourseParticipants';
import { BASE_URL } from '../../config';
import { Calendar, dateFnsLocalizer } from 'react-big-calendar';
import { format, parse, startOfWeek, getDay } from 'date-fns';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { enUS } from 'date-fns/locale/en-US';

const locales = {
  'en-US': enUS,
};

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales,
});
 
// Updated interfaces with new fields
interface Program {
  id?: string;
  courseid: number;
  scheduledate: string;
  scheduletime?: string; // new field: time portion
  venue: string;
  speakername: string;
  organizer: string;
  organizercontact1: number;
  organizercontact2: number;
  coursefees?: number; // new field for course fees
  qr_code_url?: string; // QR code URL field
}
 
interface ProgramFormValues extends Program {}
 
interface Participant {
  id: bigint;
  delegateid: number;
  registrationdate: string;
  regname: string;
  agerange: string;
  mobileno: string;
  gender: string;
  maritalstatus: string;
  occupation: string;
  area: string;
  grade: string;
}
 
const ProgramManagement: React.FC = () => {
  // Updated initial state with new fields
  const [formData, setFormData] = useState<ProgramFormValues>({
    courseid: 0,
    scheduledate: "",
    scheduletime: "",
    venue: "",
    speakername: "",
    organizer: "",
    organizercontact1: 0,
    organizercontact2: 0,
    coursefees: 0,
  });
  const [programs, setPrograms] = useState<Program[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedCourseId, setSelectedCourseId] = useState<number | null>(null);
  const [isSideWindowOpen, setIsSideWindowOpen] = useState(false);
  const [showCalendar, setShowCalendar] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Program | null>(null);
  const [showQRModal, setShowQRModal] = useState(false);
  const [selectedProgramForQR, setSelectedProgramForQR] = useState<Program | null>(null);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [isGeneratingQR, setIsGeneratingQR] = useState(false);

  const { courses, isLoadingac, errorac, refetchCourses } = useFetchAllCourses();
  const {
    data: participants,
    loading: participantsLoading,
    error: participantsError,
    fetchParticipants,
  } = useFetchCourseParticipants(selectedCourseId);
 
  // Updated calendar events (if needed you can combine date & time)
  const calendarEvents = programs.map(program => {
    // Optionally combine date and time to construct a proper Date for calendar events:
    const eventDateTime = program.scheduletime
      ? new Date(`${program.scheduledate}T${program.scheduletime}`)
      : new Date(program.scheduledate);
      
    return {
      title: courses.find(c => c.course_id === program.courseid)?.course_title || 'Unknown Course',
      start: eventDateTime,
      end: eventDateTime, // If duration is needed, you might add time to this value.
      allDay: !program.scheduletime, // if no specific time is provided, treat as all day.
      resource: program,
    };
  });
 
  // Helper to format date (you might want to also show time separately if required)
  const formatDate = (date: string) => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
 
  useEffect(() => {
    const fetchPrograms = async () => {
      try {
        const data: Program[] = await fetchAllPrograms();
        setPrograms(data);
      } catch (error) {
        console.error("Error fetching programs:", error);
        setError("Failed to fetch programs.");
      }
    };
    fetchPrograms();
  }, []);
 
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: (name === "courseid" || name === "coursefees")
        ? Number(value)
        : value,
    }));
 
    if (name === "courseid") {
      const courseId = Number(value);
      setSelectedCourseId(courseId);
      handleParticipantsClick(courseId);
    }
  };
 
  const validateDate = (date: string): boolean => {
    const selectedDate = new Date(date);
    const today = new Date();
    return selectedDate >= today;
  };
 
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
 
    if (!validateDate(formData.scheduledate)) {
      alert("Please select a future date for the program.");
      setIsLoading(false);
      return;
    }
 
    try {
      if (formData.id) {
        await updateProgramDemo(formData.id, {
          ...formData,
          organizercontact1: formData.organizercontact1,
          organizercontact2: formData.organizercontact2,
          coursefees: formData.coursefees,
          scheduletime: formData.scheduletime,
        });
        alert("Program updated successfully!");
      } else {
        await postProgramDemo({
          ...formData,
          organizercontact1: formData.organizercontact1,
          organizercontact2: formData.organizercontact2,
          coursefees: formData.coursefees,
          scheduletime: formData.scheduletime,
          addedby: 1,
          unitid: 1,
        });
        alert("Program created successfully!");
      }
 
      const data: Program[] = await fetchAllPrograms();
      setPrograms(data);
      // Reset form with new fields included
      setFormData({
        courseid: 0,
        scheduledate: "",
        scheduletime: "",
        venue: "",
        speakername: "",
        organizer: "",
        organizercontact1: 0,
        organizercontact2: 0,
        coursefees: 0,
      });
    } catch (error) {
      console.error("Error:", error);
      setError("There was an error during the operation. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };
 
  const handleEdit = (program: Program) => {
    setFormData({
      id: program.id,
      courseid: Number(program.courseid),
      scheduledate: formatDate(program.scheduledate),
      scheduletime: program.scheduletime || "",
      venue: program.venue,
      speakername: program.speakername,
      organizer: program.organizer,
      organizercontact1: program.organizercontact1,
      organizercontact2: program.organizercontact2,
      coursefees: program.coursefees || 0,
    });
  };
 
  const handleCancel = async (programId: string) => {
    if (window.confirm("Are you sure you want to cancel this program?")) {
      try {
        await cancelProgramDemo(programId, 1);
        alert("Program canceled successfully!");
        const data = await fetchAllPrograms();
        setPrograms(data);
      } catch (error) {
        console.error("Error:", error);
        setError("Failed to cancel the program.");
      }
    }
  };
 
  const handleParticipantsClick = async (courseId: number) => {
    setSelectedCourseId(courseId);
    await fetchParticipants(courseId);
    setIsSideWindowOpen(true);
  };
 
  const closeSideWindow = () => {
    setIsSideWindowOpen(false);
    setSelectedCourseId(null);
  };

  const handleGenerateQR = async (program: Program) => {
    if (!program.id) return;

    setSelectedProgramForQR(program);
    setIsGeneratingQR(true);
    setShowQRModal(true);

    try {
      // First check if QR code already exists
      if (program.qr_code_url) {
        const qrUrl = `${BASE_URL}${program.qr_code_url}`;
        console.log('Using existing QR code URL:', qrUrl);
        setQrCodeUrl(qrUrl);
        setIsGeneratingQR(false);
        return;
      }

      // Generate new QR code
      const response = await fetch(`${BASE_URL}/programs/generateqrcode`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ programId: program.id }),
      });

      const data = await response.json();
      console.log('QR generation response:', data);

      if (data.success) {
        const qrUrl = `${BASE_URL}${data.qrCodeUrl}`;
        console.log('Generated QR code URL:', qrUrl);
        setQrCodeUrl(qrUrl);
        // Update the program in the local state
        setPrograms(prev => prev.map(p =>
          p.id === program.id ? { ...p, qr_code_url: data.qrCodeUrl } : p
        ));
      } else {
        console.error('QR generation failed:', data);
        alert('Failed to generate QR code');
      }
    } catch (error) {
      console.error('Error generating QR code:', error);
      alert('Error generating QR code');
    } finally {
      setIsGeneratingQR(false);
    }
  };

  const handleDownloadQR = () => {
    if (!qrCodeUrl || !selectedProgramForQR) return;

    const link = document.createElement('a');
    link.href = qrCodeUrl;
    link.download = `program_${selectedProgramForQR.id}_qr.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const closeQRModal = () => {
    setShowQRModal(false);
    setSelectedProgramForQR(null);
    setQrCodeUrl('');
  };
 
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FFFDD0] via-[#B0C4A3] to-[#808000] p-6">
      <div className="flex justify-center mb-6">
        <button
          onClick={() => setShowCalendar(!showCalendar)}
          className="p-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          {showCalendar ? "See List View" : "See Calendar View"}
        </button>
      </div>
 
      <div className="flex items-start justify-center">
        {/* Create Program Form */}
        <div className="w-1/2 p-6 bg-white rounded shadow-xl mr-4 bg-opacity-75">
          <h2 className="text-2xl font-bold text-center mb-4">
            {formData.id ? "Edit Program" : "Create Program"}
          </h2>
          <form onSubmit={handleSubmit} className="mb-8">
            {/* Course */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">Course</label>
              <select
                name="courseid"
                value={formData.courseid}
                onChange={handleChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="">Select Course</option>
                {courses.map((g) => (
                  <option key={g.course_id} value={g.course_id}>
                    {g.course_title}
                  </option>
                ))}
              </select>
            </div>
 
            {/* Scheduled Date */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">Scheduled Date</label>
              <input
                type="date"
                name="scheduledate"
                value={formData.scheduledate}
                onChange={handleChange}
                className="w-full mt-1 p-2 border border-gray-300 rounded focus:ring-blue-300"
                required
              />
            </div>

            {/* Scheduled Time */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">Scheduled Time</label>
              <input
                type="time"
                name="scheduletime"
                value={formData.scheduletime}
                onChange={handleChange}
                className="w-full mt-1 p-2 border border-gray-300 rounded focus:ring-blue-300"
              />
            </div>

            {/* Venue */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">Venue</label>
              <input
                type="text"
                name="venue"
                value={formData.venue}
                onChange={handleChange}
                className="w-full mt-1 p-2 border border-gray-300 rounded focus:ring-blue-300"
                required
              />
            </div>
 
            {/* Speaker Name */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">Speaker Name</label>
              <input
                type="text"
                name="speakername"
                value={formData.speakername}
                onChange={handleChange}
                className="w-full mt-1 p-2 border border-gray-300 rounded focus:ring-blue-300"
                required
              />
            </div>
 
            {/* Organizer */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">Organizer</label>
              <input
                type="text"
                name="organizer"
                value={formData.organizer}
                onChange={handleChange}
                className="w-full mt-1 p-2 border border-gray-300 rounded focus:ring-blue-300"
                required
              />
            </div>
 
            {/* Organizer Contact 1 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">Organizer Contact 1</label>
              <input
                type="tel"
                name="organizercontact1"
                value={formData.organizercontact1}
                onChange={handleChange}
                className="w-full mt-1 p-2 border border-gray-300 rounded focus:ring-blue-300"
                required
              />
            </div>
 
            {/* Organizer Contact 2 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">Organizer Contact 2</label>
              <input
                type="tel"
                name="organizercontact2"
                value={formData.organizercontact2}
                onChange={handleChange}
                className="w-full mt-1 p-2 border border-gray-300 rounded focus:ring-blue-300"
                required
              />
            </div>

            {/* Course Fees */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">Course Fees</label>
              <input
                type="number"
                step="0.01"
                name="coursefees"
                value={formData.coursefees}
                onChange={handleChange}
                className="w-full mt-1 p-2 border border-gray-300 rounded focus:ring-blue-300"
              />
            </div>
 
            <button
              type="submit"
              disabled={isLoading}
              className="w-full p-2 bg-blue-500 text-white font-semibold rounded hover:bg-blue-600 focus:ring-blue-300 disabled:bg-gray-400"
            >
              {isLoading ? "Processing..." : formData.id ? "Update Program" : "Create Program"}
            </button>
 
            {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
          </form>
        </div>
 
        {/* Right Side (Calendar or Program List) */}
        <div className="w-1/2 p-6 bg-white rounded shadow-xl bg-opacity-75">
          {showCalendar ? (
            <div className="h-[700px]">
              <Calendar
                localizer={localizer}
                events={calendarEvents}
                startAccessor="start"
                endAccessor="end"
                onSelectEvent={(event) => setSelectedEvent(event.resource)}
              />
            </div>
          ) : (
            <div>
              <h3 className="text-xl font-bold mb-4">Programs List</h3>
              {programs.length === 0 ? (
                <p>No programs found.</p>
              ) : (
                <ul className="space-y-4">
                  {programs.map((program) => (
                    <li key={program.id} className="p-4 border border-gray-300 rounded">
                      <div className="flex justify-between items-center">
                        <div>
                          <p>
                            <strong>Course:</strong> {courses.find(c => c.course_id === program.courseid)?.course_title}
                          </p>
                          <p>
                            <strong>Date:</strong> {formatDate(program.scheduledate)}
                          </p>
                          <p>
                            <strong>Time:</strong> {program.scheduletime || "N/A"}
                          </p>
                          <p>
                            <strong>Venue:</strong> {program.venue}
                          </p>
                          <p>
                            <strong>Speaker:</strong> {program.speakername}
                          </p>
                          <p>
                            <strong>Fees:</strong> {program.coursefees ? program.coursefees.toFixed(2) : "0.00"}
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEdit(program)}
                            className="p-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleGenerateQR(program)}
                            className="p-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                            title="Generate QR Code"
                          >
                            QR
                          </button>
                          <button
                            onClick={() => program.id && handleCancel(program.id)}
                            className="p-2 bg-red-500 text-white rounded hover:bg-red-600"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          )}
        </div>
      </div>
 
      {/* Event Details Modal */}
      {selectedEvent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg w-full max-w-2xl">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold">Program Details</h3>
              <button
                onClick={() => setSelectedEvent(null)}
                className="text-gray-500 hover:text-gray-700"
              >
                &times;
              </button>
            </div>
            <div className="space-y-2">
              <p>
                <strong>Course:</strong> {courses.find(c => c.course_id === selectedEvent.courseid)?.course_title}
              </p>
              <p>
                <strong>Date:</strong> {formatDate(selectedEvent.scheduledate)}
              </p>
              <p>
                <strong>Time:</strong> {selectedEvent.scheduletime || "N/A"}
              </p>
              <p>
                <strong>Venue:</strong> {selectedEvent.venue}
              </p>
              <p>
                <strong>Speaker:</strong> {selectedEvent.speakername}
              </p>
              <p>
                <strong>Organizer:</strong> {selectedEvent.organizer}
              </p>
              <p>
                {/* <strong>Fees:</strong> {selectedEvent.coursefees ? selectedEvent.coursefees.toFixed(2) : "0.00"} */}
                <strong>Fees:</strong> ₹{!isNaN(Number(selectedEvent.coursefees)) ? Number(selectedEvent.coursefees).toFixed(2) : 'N/A'}
              </p>
              <div className="flex gap-2 mt-4">
                <button
                  onClick={() => {
                    handleEdit(selectedEvent);
                    setSelectedEvent(null);
                  }}
                  className="p-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
                >
                  Edit
                </button>
                <button
                  onClick={() => {
                    handleGenerateQR(selectedEvent);
                    setSelectedEvent(null);
                  }}
                  className="p-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  QR Code
                </button>
                <button
                  onClick={() => {
                    selectedEvent.id && handleCancel(selectedEvent.id);
                    setSelectedEvent(null);
                  }}
                  className="p-2 bg-red-500 text-white rounded hover:bg-red-600"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
 
      {/* Participants Window */}
      {isSideWindowOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-end">
          <div className="bg-white w-full md:w-3/4 lg:w-2/3 p-6 overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Participants List</h2>
              <button
                className="text-gray-600 hover:text-gray-800"
                onClick={closeSideWindow}
                aria-label="Close participants window"
              >
                &times;
              </button>
            </div>
            {participantsLoading && (
              <div className="text-center">Loading participants...</div>
            )}
            {participantsError && (
              <div className="text-center text-red-500">
                Error loading participants
              </div>
            )}
            {!participantsLoading &&
              !participantsError &&
              participants.length === 0 && (
                <div className="text-center text-gray-500">
                  No participants found for this course.
                </div>
              )}
            {!participantsLoading &&
              !participantsError &&
              participants.length > 0 && (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="p-2 border min-w-[100px]">ID</th>
                        <th className="p-2 border min-w-[150px]">Registration Date</th>
                        <th className="p-2 border min-w-[200px]">Name</th>
                        <th className="p-2 border min-w-[100px]">Age Range</th>
                        <th className="p-2 border min-w-[150px]">Mobile No</th>
                        <th className="p-2 border min-w-[100px]">Gender</th>
                        <th className="p-2 border min-w-[150px]">Marital Status</th>
                        <th className="p-2 border min-w-[150px]">Occupation</th>
                        <th className="p-2 border min-w-[150px]">Area</th>
                        <th className="p-2 border min-w-[100px]">Grade</th>
                      </tr>
                    </thead>
                    <tbody>
                      {participants.map((participant) => (
                        <tr key={participant.id} className="border">
                          <td className="p-2 border">{participant.delegateid}</td>
                          <td className="p-2 border">{participant.registrationdate}</td>
                          <td className="p-2 border">{participant.regname}</td>
                          <td className="p-2 border">{participant.agerange}</td>
                          <td className="p-2 border">{participant.mobileno}</td>
                          <td className="p-2 border">{participant.gender}</td>
                          <td className="p-2 border">{participant.maritalstatus}</td>
                          <td className="p-2 border">{participant.occupation}</td>
                          <td className="p-2 border">{participant.area}</td>
                          <td className="p-2 border">{participant.grade}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
          </div>
        </div>
      )}

      {/* QR Code Modal */}
      {showQRModal && selectedProgramForQR && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold">Program QR Code</h3>
              <button
                onClick={closeQRModal}
                className="text-gray-500 hover:text-gray-700 text-2xl"
              >
                &times;
              </button>
            </div>

            <div className="text-center">
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">
                  <strong>Course:</strong> {courses.find(c => c.course_id === selectedProgramForQR.courseid)?.course_title}
                </p>
                <p className="text-sm text-gray-600 mb-2">
                  <strong>Date:</strong> {formatDate(selectedProgramForQR.scheduledate)}
                </p>
                <p className="text-sm text-gray-600 mb-4">
                  <strong>Venue:</strong> {selectedProgramForQR.venue}
                </p>
              </div>

              {isGeneratingQR ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  <span className="ml-2">Generating QR Code...</span>
                </div>
              ) : qrCodeUrl ? (
                <div>
                  <div className="mb-2">
                    <p className="text-xs text-gray-400">QR Code URL: {qrCodeUrl}</p>
                  </div>
                  <img
                    src={qrCodeUrl}
                    alt="Program QR Code"
                    className="mx-auto mb-4 border border-gray-300 rounded"
                    style={{ width: '200px', height: '200px' }}
                    onLoad={() => console.log('QR code image loaded successfully')}
                    onError={(e) => {
                      console.error('QR code image failed to load:', e);
                      console.error('Failed URL:', qrCodeUrl);
                    }}
                  />
                  <p className="text-xs text-gray-500 mb-4">
                    Scan this QR code to access the program registration page
                  </p>
                  <div className="flex gap-2 justify-center">
                    <button
                      onClick={handleDownloadQR}
                      className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                      Download QR Code
                    </button>
                    <button
                      onClick={() => {
                        const programUrl = `${window.location.origin}/wellness/course-info/${selectedProgramForQR.id}`;
                        navigator.clipboard.writeText(programUrl);
                        alert('Program URL copied to clipboard!');
                      }}
                      className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                    >
                      Copy URL
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-red-500">Failed to generate QR code</div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
 
export default ProgramManagement;
