import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle, Typography, Button } from './ui';

export function PaymentConfirmation() {
  const [suggestion, setSuggestion] = useState('');
  const [feedbackSent, setFeedbackSent] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Get payment data from navigation state
  const paymentData = location.state?.paymentData;
  const courseTitle = location.state?.courseTitle;
  const amount = location.state?.amount;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Replace with API call to send suggestion
    console.log('Suggestion submitted:', suggestion);
    setSuggestion('');
    setFeedbackSent(true);
    // Optionally, clear localStorage or do any cleanup here
    // localStorage.clear();
    // Do not navigate immediately to welcome; let user choose
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-neutral-50 via-white to-primary-50 py-10 px-4">
      <Card variant="elevated" className="max-w-2xl w-full">
        <CardHeader className="text-center">
          {/* Success Icon */}
          <div className="w-20 h-20 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-10 h-10 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>

          <CardTitle className="text-3xl text-secondary-800 mb-2">
            🎉 Payment Successful!
          </CardTitle>
          <Typography variant="body1" color="neutral" className="mb-4">
            Thank you for your enrollment. Your payment has been received successfully.
          </Typography>
          <Typography variant="body2" color="neutral">
            Our team will reach out to you soon on WhatsApp.
          </Typography>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Payment Details */}
          {paymentData && (
            <div className="bg-neutral-50 rounded-lg p-4 space-y-3">
              <Typography variant="h6" weight="bold" color="primary" className="mb-3">
                Payment Details
              </Typography>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div>
                  <Typography variant="body2" weight="medium" color="neutral">
                    Course:
                  </Typography>
                  <Typography variant="body2" color="primary">
                    {courseTitle || 'N/A'}
                  </Typography>
                </div>

                <div>
                  <Typography variant="body2" weight="medium" color="neutral">
                    Amount Paid:
                  </Typography>
                  <Typography variant="body2" color="primary" weight="bold">
                    ₹ {amount || 'N/A'}
                  </Typography>
                </div>

                <div>
                  <Typography variant="body2" weight="medium" color="neutral">
                    Payment ID:
                  </Typography>
                  <Typography variant="body2" color="primary" className="font-mono text-xs">
                    {paymentData.payment_id || 'N/A'}
                  </Typography>
                </div>

                <div>
                  <Typography variant="body2" weight="medium" color="neutral">
                    Order ID:
                  </Typography>
                  <Typography variant="body2" color="primary" className="font-mono text-xs">
                    {paymentData.order_id || 'N/A'}
                  </Typography>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            <Typography variant="body2" weight="medium" color="neutral" className="mb-3">
              What would you like to do next?
            </Typography>

            <Button
              onClick={() => navigate('/courseselection')}
              variant="primary"
              size="lg"
              fullWidth
              className="mb-3"
            >
              Explore More Programs
            </Button>

            <Button
              onClick={() => navigate('/welcome')}
              variant="secondary"
              size="lg"
              fullWidth
            >
              Exit to Home
            </Button>
          </div>

          {/* Feedback Section */}
          <div className="border-t border-neutral-200 pt-6">
            <Typography variant="body2" weight="medium" color="neutral" className="mb-3">
              We value your feedback!
            </Typography>

            <form onSubmit={handleSubmit} className="space-y-3">
              <textarea
                id="suggestion"
                name="suggestion"
                rows={3}
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg text-neutral-900 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:border-secondary-500"
                placeholder="Let us know your suggestions or feedback..."
                value={suggestion}
                onChange={(e) => setSuggestion(e.target.value)}
                disabled={feedbackSent}
              />

              <Button
                type="submit"
                variant={feedbackSent ? "accent" : "primary"}
                size="md"
                fullWidth
                disabled={feedbackSent}
              >
                {feedbackSent ? "✓ Feedback Sent" : "Submit Feedback"}
              </Button>
            </form>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
