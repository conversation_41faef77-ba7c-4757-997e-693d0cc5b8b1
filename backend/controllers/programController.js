const database = require('../services/database')
const qrCodeService = require('../services/qrCodeService')

exports.getAllPrograms = async (req, res) => {
    try {
      const result = await database.pool.query(`
        SELECT pd.id, pd.courseid, pd.scheduledate, pd.scheduletime, pd.coursefees,
               pd.venue, pd.speakername, pd.organizer, pd.organizercontact1, pd.organizercontact2,
               pd.isactive, pd.iscancelled, pd.isupdated, pd.updatedversion,
               pd.addedby, pd.updatedby, pd.cancelledby, pd.createddate,
               pd.updateddate, pd.cancelleddate, pd.versionid, pd.unitid, pd.qr_code_url
        FROM programdet pd
        WHERE isactive = true
      `);
  
      return res.status(200).json(result.rows);
    } catch (error) {
      return res.status(500).json({ error: error.message });
    }
  };
  

exports.createProgram = async (req, res) => {
    try {
      if (!req.body.courseid) {
        return res.status(422).json({ error: 'Course ID is required' });
      }
  
      if (!req.body.venue) {
        return res.status(422).json({ error: 'Venue is required' });
      }
  
      if (!req.body.addedby) {
        return res.status(422).json({ error: 'Added by (user) is required' });
      }
  
      const result = await database.pool.query({
        text: `
          INSERT INTO programdet (
            courseid, scheduledate, scheduletime, coursefees, venue, speakername, organizer, 
            organizercontact1, organizercontact2, isactive, 
            addedby, updatedversion, versionid, unitid, createddate
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
            $11, $12, $13, $14, CURRENT_TIMESTAMP
          ) RETURNING *`,
        values: [
          req.body.courseid,
          req.body.scheduledate || null,
          req.body.scheduletime || null,
          req.body.coursefees || null,
          req.body.venue,
          req.body.speakername || null,
          req.body.organizer || null,
          req.body.organizercontact1 || null,
          req.body.organizercontact2 || null,
          'isactive' in req.body ? req.body.isactive : true,
          req.body.addedby,
          req.body.updatedversion || 1,
          req.body.versionid || 1,
          req.body.unitid || null
        ]
      });
  
      // Insert course delegates
      await database.pool.query({
        text: `SELECT public.func_insert_programdelegates($1, $2);`,
        values: [result.rows[0].id, result.rows[0].courseid]
      });

      // Generate QR code for the program
      try {
        const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
        const qrCodeData = await qrCodeService.generateProgramQRCode(result.rows[0].id, baseUrl);

        // Update the program with QR code URL
        await database.pool.query({
          text: `UPDATE programdet SET qr_code_url = $1 WHERE id = $2`,
          values: [qrCodeData.qrCodeUrl, result.rows[0].id]
        });

        // Add QR code info to the response
        result.rows[0].qr_code_url = qrCodeData.qrCodeUrl;
        result.rows[0].program_url = qrCodeData.programUrl;
      } catch (qrError) {
        console.error('Error generating QR code:', qrError);
        // Don't fail the program creation if QR code generation fails
      }

      return res.status(201).json(result.rows[0]);
    } catch (error) {
      return res.status(500).json({ error: error.message });
    }
  };
  

exports.createProgramDelegates = async (req, res) => {
    try {
        if (!req.body.courseid) {
            return res.status(422).json({ error: 'Course ID is required' });
        }

        const result = await database.pool.query({
            text: `
                INSERT INTO programdelegates
                (programid, delegateid, iscontacted, isconfirmed, ispaymentreceived, iscancelled, isattended)
                ) VALUES (
                    $1, $2, true,false,false,false,false)
                ) RETURNING *`,
            values: [
                req.body.programid,
                req.body.delegateid
            ]
        });

        return res.status(201).json(result.rows[0]);
    } catch (error) {
        return res.status(500).json({ error: error.message });
    }
}

exports.updateProgram = async (req, res) => {
    try {
      const requiredFields = ['courseid', 'venue'];
  
      for (const field of requiredFields) {
        if (!req.body[field]) {
          return res.status(422).json({ error: `${field} is required` });
        }
      }
  
      const result = await database.pool.query({
        text: `
          UPDATE programdet
          SET 
            courseid = $1,
            scheduledate = $2,
            scheduletime = $3,
            coursefees = $4,
            venue = $5,
            speakername = $6,
            organizer = $7,
            organizercontact1 = $8,
            organizercontact2 = $9,
            isactive = $10,
            isupdated = true,
            updatedversion = updatedversion + 1,
            updatedby = $11,
            updateddate = CURRENT_TIMESTAMP,
            addedby = 1
          WHERE id = $12
          RETURNING *`,
        values: [
          req.body.courseid,
          req.body.scheduledate || null,
          req.body.scheduletime || null,
          req.body.coursefees || null,
          req.body.venue,
          req.body.speakername || null,
          req.body.organizer || null,
          req.body.organizercontact1 || null,
          req.body.organizercontact2 || null,
          req.body.isactive || true,
          req.body.updatedby || 1,
          req.params.id
        ]
      });
  
      if (result.rowCount === 0) {
        return res.status(404).json({ error: 'Program not found' });
      }
  
      return res.status(200).json(result.rows[0]);
    } catch (error) {
      return res.status(500).json({ error: error.message });
    }
  };
  

exports.cancelProgram = async (req, res) => {
    try {
        // Validate if `cancelledby` is provided in the request body
        if (!req.body.cancelledby) {
            return res.status(422).json({ error: 'Cancelled by (user) is required' });
        }

        

        const result = await database.pool.query({
            text: `
                UPDATE programdet
                SET iscancelled = true, 
                    cancelledby = $1, 
                    cancelleddate = CURRENT_TIMESTAMP ,
                    isactive = false
                WHERE id = $2
                RETURNING *`,
            values: [req.body.cancelledby, req.body.id]
        });

        return res.status(200).json(result.rows[0]);
    } catch (error) {
        return res.status(500).json({ error: error.message });
    }
}


exports.getProgramById = async (req, res) => {


  console.log(req.body);

    try {
      const result = await database.pool.query({
        text: `
          SELECT pd.id, pd.courseid, pd.scheduledate, pd.scheduletime, pd.coursefees,
                 pd.venue, pd.speakername, pd.organizer, pd.organizercontact1, pd.organizercontact2,
                 pd.isactive, pd.iscancelled, pd.isupdated, pd.updatedversion,
                 pd.addedby, pd.updatedby, pd.cancelledby, pd.createddate,
                 pd.updateddate, pd.cancelleddate, pd.versionid, pd.unitid, pd.qr_code_url,
                 fc.course_code,
                 fc.course_title,
                 fc.course_shortdetail,
                 fc.course_content,
            	 fc.course_category,
            	 fc.course_imagepath
          FROM programdet pd
          JOIN LATERAL
    	public.func_specificcourse(pd.courseid) AS fc ON TRUE
          WHERE pd.id = $1
        `,
        values: [req.body.programId]
      });
  
      if (result.rowCount === 0) {
        return res.status(404).json({ error: 'Program not found' });
      }
  
      return res.status(200).json(result.rows[0]);
    } catch (error) {
      return res.status(500).json({ error: error.message });
    }
  };
  


exports.getDelegateByProgramId = async (req, res) => {
    try {

        const result = await database.pool.query({
            text: `select * from public.func_getprogramdelegates($1)`,
            values: [req.body.programid]
        });

        // console.log(result);

        if (result.rowCount === 0) {
            return res.status(404).json({ error: 'Program Delegates not found' });
        }

        return res.status(200).json(result.rows);
    } catch (error) {
        return res.status(500).json({ error: error.message });
    }
}

exports.updateDelegateStatus = async (req, res) => {
    const { delegateid, programid, ...updates } = req.body;
  
    try {
      // Construct the SQL query dynamically based on the fields to update
      const fields = Object.keys(updates)
        .map((key, index) => `${key} = $${index + 3}`)
        .join(', ');

        
      const query = {
        text: `UPDATE programdelegates SET ${fields} WHERE delegateid = $1 AND programid = $2`,
        values: [delegateid, programid, ...Object.values(updates)],
      };

    //   console.log(query);
  
      await database.pool.query(query);
      res.status(200).json({ message: 'Delegate status updated successfully.' });
    } catch (error) {
      console.error('Error updating delegate status:', error);
      res.status(500).json({ error: 'Failed to update delegate status.' });
    }
  };

  exports.getProgramsByCourseId = async (req, res) => {
    try {
      const result = await database.pool.query({
        text: `
          SELECT pd.id, pd.courseid, pd.scheduledate, pd.scheduletime, pd.coursefees,
                 pd.venue, pd.speakername, pd.organizer, pd.organizercontact1, pd.organizercontact2,
                 pd.isactive, pd.iscancelled, pd.isupdated, pd.updatedversion,
                 pd.addedby, pd.updatedby, pd.cancelledby, pd.createddate,
                 pd.updateddate, pd.cancelleddate, pd.versionid, pd.unitid
          FROM programdet pd
          WHERE pd.courseid = $1
        `,
        values: [req.params.courseId]
      });

      return res.status(200).json(result.rows);
    } catch (error) {
      return res.status(500).json({ error: error.message });
    }
  };

// Generate QR code for an existing program
exports.generateQRCode = async (req, res) => {
  try {
    const { programId } = req.body;

    if (!programId) {
      return res.status(422).json({ error: 'Program ID is required' });
    }

    // Check if program exists
    const programResult = await database.pool.query({
      text: 'SELECT id FROM programdet WHERE id = $1 AND isactive = true',
      values: [programId]
    });

    if (programResult.rowCount === 0) {
      return res.status(404).json({ error: 'Program not found' });
    }

    // Generate QR code
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    const qrCodeData = await qrCodeService.generateProgramQRCode(programId, baseUrl);

    // Update the program with QR code URL
    await database.pool.query({
      text: `UPDATE programdet SET qr_code_url = $1 WHERE id = $2`,
      values: [qrCodeData.qrCodeUrl, programId]
    });

    return res.status(200).json({
      success: true,
      programId,
      qrCodeUrl: qrCodeData.qrCodeUrl,
      programUrl: qrCodeData.programUrl
    });
  } catch (error) {
    console.error('Error generating QR code:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get QR code for a program
exports.getQRCode = async (req, res) => {
  try {
    const { programId } = req.params;

    if (!programId) {
      return res.status(422).json({ error: 'Program ID is required' });
    }

    // Get program with QR code
    const result = await database.pool.query({
      text: 'SELECT id, qr_code_url FROM programdet WHERE id = $1 AND isactive = true',
      values: [programId]
    });

    if (result.rowCount === 0) {
      return res.status(404).json({ error: 'Program not found' });
    }

    const program = result.rows[0];

    // If QR code doesn't exist, generate it
    if (!program.qr_code_url) {
      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const qrCodeData = await qrCodeService.generateProgramQRCode(programId, baseUrl);

      // Update the program with QR code URL
      await database.pool.query({
        text: `UPDATE programdet SET qr_code_url = $1 WHERE id = $2`,
        values: [qrCodeData.qrCodeUrl, programId]
      });

      program.qr_code_url = qrCodeData.qrCodeUrl;
      program.program_url = qrCodeData.programUrl;
    }

    return res.status(200).json({
      success: true,
      programId,
      qrCodeUrl: program.qr_code_url,
      programUrl: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/wellness/course-info/${programId}`
    });
  } catch (error) {
    console.error('Error getting QR code:', error);
    return res.status(500).json({ error: error.message });
  }
};
  