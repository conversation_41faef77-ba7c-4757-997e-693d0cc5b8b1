const router = require('express').Router();
const Razorpay = require('razorpay');
const crypto = require('crypto');

// Initialize Razorpay with Test Mode Keys
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID || 'rzp_test_d7NTm2Fmuuqwld',
  key_secret: process.env.RAZORPAY_SECRET || 'Pwy68l9uw9ibA5rJthaUdfYY',
});

// Create Razorpay Order
router.post("/create-order", async (req, res) => {
  try {
    const { amount, receipt, notes } = req.body;

    // Validate required fields
    if (!amount || amount <= 0) {
      return res.status(400).json({
        error: "Amount is required and must be greater than 0"
      });
    }

    const options = {
      amount: Math.round(amount * 100), // Convert to paisa and ensure integer
      currency: "INR",
      receipt: receipt || `receipt_${Date.now()}`,
      notes: notes || {},
    };

    const order = await razorpay.orders.create(options);

    res.status(200).json({
      success: true,
      order: order,
      key_id: 'rzp_test_d7NTm2Fmuuqwld'
    });
  } catch (error) {
    console.error('Order creation error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Verify Payment
router.post("/verify-payment", async (req, res) => {
  try {
    const {
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
      course_id,
      delegate_id
    } = req.body;

    // Validate required fields
    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      return res.status(400).json({
        success: false,
        error: "Missing required payment verification fields"
      });
    }

    // Create signature for verification
    const body = razorpay_order_id + "|" + razorpay_payment_id;
    const expectedSignature = crypto
      .createHmac("sha256", 'Pwy68l9uw9ibA5rJthaUdfYY')
      .update(body.toString())
      .digest("hex");

    const isAuthentic = expectedSignature === razorpay_signature;

    if (isAuthentic) {
      // Payment is verified - you can save to database here
      // TODO: Save payment details to database

      res.status(200).json({
        success: true,
        message: "Payment verified successfully",
        payment_id: razorpay_payment_id,
        order_id: razorpay_order_id
      });
    } else {
      res.status(400).json({
        success: false,
        error: "Payment verification failed"
      });
    }
  } catch (error) {
    console.error('Payment verification error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Handle Payment Failure
router.post("/payment-failed", async (req, res) => {
  try {
    const { error, order_id } = req.body;

    // Log payment failure
    console.log('Payment failed:', { error, order_id });

    // TODO: Update database with failed payment status

    res.status(200).json({
      success: true,
      message: "Payment failure recorded"
    });
  } catch (error) {
    console.error('Payment failure handling error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
