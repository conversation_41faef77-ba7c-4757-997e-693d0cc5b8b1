const axios = require('axios');

async function testQRCodeGeneration() {
  try {
    console.log('Testing QR code generation...');
    
    // Test generating QR code for program ID 12 (from your example URL)
    const response = await axios.post('http://localhost:3008/programs/generateqrcode', {
      programId: 12
    });
    
    console.log('✅ QR Code generation successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    // Test getting QR code
    const getResponse = await axios.get('http://localhost:3008/programs/qrcode/12');
    console.log('✅ QR Code retrieval successful!');
    console.log('Get Response:', JSON.stringify(getResponse.data, null, 2));
    
  } catch (error) {
    console.error('❌ Error testing QR code:', error.response?.data || error.message);
  }
}

testQRCodeGeneration();
