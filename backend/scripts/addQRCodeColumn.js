const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration
const pool = new Pool({
  host: 'localhost',
  user: 'postgres',
  password: '<PERSON>@108',
  port: '5432',
  database: 'spcrm_uat'
});

async function addQRCodeColumn() {
  try {
    console.log('Connecting to database...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../sql/add_qr_code_column.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    console.log('Executing SQL migration...');
    console.log(sql);
    
    // Execute the SQL
    await pool.query(sql);
    
    console.log('✅ QR code column added successfully!');
    
    // Verify the column was added
    const result = await pool.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'programdet' AND column_name = 'qr_code_url'
    `);
    
    if (result.rows.length > 0) {
      console.log('✅ Column verification successful:', result.rows[0]);
    } else {
      console.log('❌ Column verification failed - column not found');
    }
    
  } catch (error) {
    console.error('❌ Error adding QR code column:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
    console.log('Database connection closed.');
  }
}

// Run the migration
addQRCodeColumn();
