const axios = require('axios');

async function testProgramCreation() {
  try {
    console.log('Testing program creation with automatic QR code generation...');
    
    // Create a new program
    const programData = {
      courseid: 1, // Assuming course ID 1 exists
      scheduledate: '2025-07-01',
      scheduletime: '10:00',
      venue: 'Test Venue',
      speakername: 'Test Speaker',
      organizer: 'Test Organizer',
      organizercontact1: 1234567890,
      organizercontact2: 9876543210,
      coursefees: 500,
      addedby: 1,
      unitid: 1
    };
    
    const response = await axios.post('http://localhost:3008/programs/createprogram', programData);
    
    console.log('✅ Program created successfully!');
    console.log('Program ID:', response.data.id);
    console.log('QR Code URL:', response.data.qr_code_url);
    console.log('Program URL:', response.data.program_url);
    
    // Test getting the program by ID to verify QR code is included
    const getProgramResponse = await axios.post('http://localhost:3008/programs/searchprogrambyid', {
      programId: response.data.id
    });
    
    console.log('✅ Program retrieval successful!');
    console.log('Retrieved QR Code URL:', getProgramResponse.data.qr_code_url);
    
  } catch (error) {
    console.error('❌ Error testing program creation:', error.response?.data || error.message);
  }
}

testProgramCreation();
