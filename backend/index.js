const express = require('express');
const http = require('http');
const bodyParser = require('body-parser');
require('dotenv').config();
const cors = require('cors');
const { createProxyMiddleware } = require('http-proxy-middleware');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const fs = require('fs');
const path = require('path');
const moment = require('moment-timezone');

const port = process.env.PORT || 3008;


// console.log(new Date());

// const currentTime = new Date().toString();
// console.log("Current time:", currentTime);


const allowedOrigins = [
  'http://*************:5173',
  'http://localhost:5173',
  'http://*************:5173',
  'http://************:5173',
  'http://************:5173',
  'http://*************:5020',
  'http://************:5173',
  'http://*************',
  'https://wellness.bhaktivedantahospital.com',
  'http://wellness.bhaktivedantahospital.com',
  'https://wellness.bhaktivedantahospital.com:5173/',
  'http://wellness.bhaktivedantahospital.com:5173/',
  'wss://wellness.bhaktivedantahospital.com:5173/',
  'https://*************/',
  'https://wb.omni.tatatelebusiness.com/whatsapp-cloud/'
];

const app = express();
app.use(express.json())
app.use(bodyParser.json());


// Logging
// app.use(morgan('combined'));

// Create a write stream (in append mode) for logging
const accessLogStream = fs.createWriteStream(path.join(__dirname, 'access.log'), { flags: 'a' });


// Custom token for local time
morgan.token('local-time', () => new Date().toLocaleString());

// Use the custom token in your morgan format
app.use(morgan(':method :url :status - :local-time'));

// Use morgan to log requests
app.use(morgan('combined', { stream: accessLogStream }));

// Trust the proxy header
app.set('trust proxy', 1);

// Rate Limiting
// const limiter = rateLimit({
//   windowMs: 60 * 60 * 1000, // 15 minutes
//   max: 100, // Limit each IP to 100 requests per window
//   message: 'Too many requests, please try again later.'
// });
// app.use(limiter);

// Security Headers with relaxed CSP for images
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https:"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "http:", "https:"], // Allow images from any HTTP/HTTPS source
      fontSrc: ["'self'", "https:", "data:"],
      connectSrc: ["'self'", "http:", "https:"],
    },
  },
}));

// CORS Configuration
app.use(cors({
  origin: allowedOrigins,
  methods: 'GET,POST,PUT,DELETE', // Allow the necessary HTTP methods
  allowedHeaders: 'Content-Type,Authorization', // Allow custom headers if needed
}));

// Middleware to add the current date to the request object
app.use((req, res, next) => {
  req.currentDate = moment().tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss'); // Replace 'Asia/Kolkata' with your desired time zone
  next();
});


// // Proxy Middleware
// app.use('/api', createProxyMiddleware({
//   target: 'http://*************:3008',
//   // changeOrigin: true,
//   secure: false,
//   logLevel: 'debug',
//   onError: (err, req, res) => {
//     console.error('Proxy error:', err);
//     res.status(500).send('Proxy error');
//   }
// }));

// Serve static files from public directory
app.use(express.static(path.join(__dirname, 'public')));

// Routes
app.use('/categories', require('./routes/categoryRoute'));
app.use('/programs', require('./routes/programRoute'));
app.use('/GeneralMasters', require('./routes/GeneralMastersRoute'));
app.use('/auth', require('./routes/authRoute'));
app.use('/Delegates', require('./routes/DelegateRegistrationRoute'));
app.use('/sendotp', require('./routes/otpRoute'));
app.use('/payment', require('./routes/payment'));


// Error Handling Middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Internal Server Error',
  });
});

app.listen(port, () => console.log(`Server started on port ${port}`));
