{"name": "rest_api_nodejs_postgres", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.7.9", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.2", "express-rate-limit": "^7.4.1", "express-session": "^1.18.1", "helmet": "^8.0.0", "http-proxy-middleware": "^3.0.3", "jsonwebtoken": "^9.0.2", "moment-timezone": "^0.5.46", "morgan": "^1.10.0", "pg": "^8.11.5", "razorpay": "^2.9.6"}, "devDependencies": {"nodemon": "^3.0.3"}}