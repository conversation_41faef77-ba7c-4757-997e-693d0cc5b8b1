const QRCode = require('qrcode');
const path = require('path');
const fs = require('fs');

class QRCodeService {
  constructor() {
    // Create QR codes directory if it doesn't exist
    this.qrCodesDir = path.join(__dirname, '../public/qrcodes');
    if (!fs.existsSync(this.qrCodesDir)) {
      fs.mkdirSync(this.qrCodesDir, { recursive: true });
    }
  }

  /**
   * Generate QR code for a program URL
   * @param {number} programId - The program ID
   * @param {string} baseUrl - Base URL for the application (e.g., 'http://localhost:5173')
   * @returns {Promise<{qrCodeUrl: string, qrCodePath: string}>}
   */
  async generateProgramQRCode(programId, baseUrl = 'http://localhost:5173') {
    try {
      // Construct the program URL
      const programUrl = `${baseUrl}/wellness/course-info/${programId}`;
      
      // Generate filename for QR code
      const fileName = `program_${programId}_qr.png`;
      const filePath = path.join(this.qrCodesDir, fileName);
      
      // QR code options
      const options = {
        type: 'png',
        quality: 0.92,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        width: 256
      };

      // Generate QR code and save to file
      await QRCode.toFile(filePath, programUrl, options);
      
      // Return the URL path and file path
      const qrCodeUrl = `/qrcodes/${fileName}`;
      
      return {
        qrCodeUrl,
        qrCodePath: filePath,
        programUrl
      };
    } catch (error) {
      console.error('Error generating QR code:', error);
      throw new Error('Failed to generate QR code');
    }
  }

  /**
   * Generate QR code as base64 data URL
   * @param {number} programId - The program ID
   * @param {string} baseUrl - Base URL for the application
   * @returns {Promise<string>} Base64 data URL
   */
  async generateProgramQRCodeDataURL(programId, baseUrl = 'http://localhost:5173') {
    try {
      const programUrl = `${baseUrl}/wellness/course-info/${programId}`;
      
      const options = {
        type: 'png',
        quality: 0.92,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        width: 256
      };

      // Generate QR code as data URL
      const dataURL = await QRCode.toDataURL(programUrl, options);
      return dataURL;
    } catch (error) {
      console.error('Error generating QR code data URL:', error);
      throw new Error('Failed to generate QR code data URL');
    }
  }

  /**
   * Delete QR code file for a program
   * @param {number} programId - The program ID
   */
  async deleteProgramQRCode(programId) {
    try {
      const fileName = `program_${programId}_qr.png`;
      const filePath = path.join(this.qrCodesDir, fileName);
      
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`QR code deleted for program ${programId}`);
      }
    } catch (error) {
      console.error('Error deleting QR code:', error);
    }
  }

  /**
   * Check if QR code exists for a program
   * @param {number} programId - The program ID
   * @returns {boolean}
   */
  qrCodeExists(programId) {
    const fileName = `program_${programId}_qr.png`;
    const filePath = path.join(this.qrCodesDir, fileName);
    return fs.existsSync(filePath);
  }
}

module.exports = new QRCodeService();
