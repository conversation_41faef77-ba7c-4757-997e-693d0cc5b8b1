<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .qr-container {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        img {
            border: 1px solid #ddd;
            max-width: 300px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>QR Code Loading Test</h1>
    
    <div class="qr-container">
        <h3>Test 1: Direct QR Code Image</h3>
        <p>URL: <code>http://localhost:3008/qrcodes/program_12_qr.png</code></p>
        <img 
            src="http://localhost:3008/qrcodes/program_12_qr.png" 
            alt="QR Code Test"
            onload="document.getElementById('status1').innerHTML = 'Image loaded successfully!'; document.getElementById('status1').className = 'success';"
            onerror="document.getElementById('status1').innerHTML = 'Image failed to load!'; document.getElementById('status1').className = 'error';"
        />
        <p id="status1">Loading...</p>
    </div>

    <div class="qr-container">
        <h3>Test 2: Relative QR Code Image</h3>
        <p>URL: <code>/qrcodes/program_12_qr.png</code></p>
        <img 
            src="/qrcodes/program_12_qr.png" 
            alt="QR Code Test Relative"
            onload="document.getElementById('status2').innerHTML = 'Image loaded successfully!'; document.getElementById('status2').className = 'success';"
            onerror="document.getElementById('status2').innerHTML = 'Image failed to load!'; document.getElementById('status2').className = 'error';"
        />
        <p id="status2">Loading...</p>
    </div>

    <div class="qr-container">
        <h3>Test 3: Fetch API Test</h3>
        <button onclick="testFetch()">Test Fetch API</button>
        <p id="fetchStatus">Click button to test</p>
        <div id="fetchResult"></div>
    </div>

    <script>
        async function testFetch() {
            const statusEl = document.getElementById('fetchStatus');
            const resultEl = document.getElementById('fetchResult');
            
            try {
                statusEl.innerHTML = 'Testing fetch...';
                
                const response = await fetch('http://localhost:3008/programs/generateqrcode', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ programId: 12 }),
                });
                
                const data = await response.json();
                console.log('Fetch response:', data);
                
                if (data.success) {
                    statusEl.innerHTML = 'Fetch successful!';
                    statusEl.className = 'success';
                    
                    const qrUrl = `http://localhost:3008${data.qrCodeUrl}`;
                    resultEl.innerHTML = `
                        <p>QR URL: <code>${qrUrl}</code></p>
                        <img src="${qrUrl}" alt="Fetched QR Code" style="max-width: 200px;" 
                             onload="console.log('Fetched image loaded')"
                             onerror="console.error('Fetched image failed to load')" />
                    `;
                } else {
                    statusEl.innerHTML = 'Fetch failed!';
                    statusEl.className = 'error';
                    resultEl.innerHTML = `<p>Error: ${JSON.stringify(data)}</p>`;
                }
            } catch (error) {
                console.error('Fetch error:', error);
                statusEl.innerHTML = 'Fetch error!';
                statusEl.className = 'error';
                resultEl.innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
